{%- render 'section-slideshow-room' -%}

{% schema %}
{
"name": "section-slideshow-room",
"class": "index-section--hero",
"settings": [
  {
    "type": "checkbox",
    "id": "full_width",
    "label": "t:labels.full_page_width",
    "default": true
  },
  {
    "type": "range",
    "id": "height",
    "label": "t:labels.desktop_height",
    "default": 650,
    "min": 450,
    "max": 750,
    "step": 10,
    "unit": "px"
  },
  {
    "type": "range",
    "id": "height_mobile",
    "label": "t:labels.mobile_height",
    "default": 450,
    "min": 350,
    "max": 650,
    "step": 10,
    "unit": "px"
  },
  {
    "type": "select",
    "id": "style",
    "label": "t:labels.slide_navigation_style",
    "default": "minimal",
    "options": [
      {
        "value": "minimal",
        "label": "t:labels.minimal"
      },
      {
        "value": "arrows",
        "label": "t:labels.arrows"
      },
      {
        "value": "bars",
        "label": "t:labels.bars"
      },
      {
        "value": "dots",
        "label": "t:labels.dots"
      }
    ]
  },
  {
    "type": "checkbox",
    "id": "autoplay",
    "label": "t:labels.autochange_slides",
    "default": true
  },
  {
    "type": "range",
    "id": "autoplay_speed",
    "label": "t:labels.change_images_every",
    "default": 7,
    "min": 5,
    "max": 10,
    "step": 1,
    "unit": "s"
  },
  {
    "type": "image_picker",
    "id": "right_image",
    "label": "Right Image (1015x647px)"
  },
  {
    "type": "url",
    "id": "right_link",
    "label": "Right Image Link"
  }
],
"blocks": [
  {
    "type": "slide",
    "name": "t:labels.slide",
    "settings": [

      {
        "type": "image_picker",
        "id": "image",
        "label": "Left Carousel Image (647x647px)"
      },
      {
        "type": "url",
        "id": "link",
        "label": "Left Image Link"
      }
    ]
  }
],
"max_blocks": 5,
"presets": [
  {
    "name": "section-slideshow-room",
    "blocks": [
      {
        "type": "slide",
        "settings": {
        }
      }
    ]
  }
],
"disabled_on": {
  "groups": [
    "footer",
    "header",
    "custom.popups"
  ]
}
}
{% endschema %}
