{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a slideshow room section with dual images layout.

  Accepts:
  - full_width {boolean} - Whether to make the section full width
  - autoplay {boolean} - Whether to autoplay the slideshow
  - autoplay_speed {5-10} - The speed at which the slideshow should autoplay
  - style {'minimal'|'arrows'|'bars'|'dots'} - The style of the slideshow
  - height {450-750} - The height of the slideshow
  - height_mobile {350-650} - The height of the slideshow on mobile
  - hydration {string} - The hydration strategy for the section

  Features:
  - Left side: Carousel with multiple images (647px × 647px each)
  - Right side: Single static image (1015px × 647px)
  - Gap between images: 58px
  - Responsive design for mobile and tablet

  Usage:
  {% render 'section-slideshow-room' %}
{%- endcomment -%}

{%- liquid
  assign full_width = full_width | default: section.settings.full_width, allow_false: true | default: true, allow_false: true
  assign autoplay = autoplay | default: section.settings.autoplay, allow_false: true | default: false, allow_false: true
  assign autoplay_speed = autoplay_speed | default: section.settings.autoplay_speed | default: 7
  assign style = style | default: section.settings.style | default: 'minimal'
  assign height = height | default: section.settings.height | default: 650
  assign height_mobile = height_mobile | default: section.settings.height_mobile | default: 450
  assign hydration = hydration | default: 'on:visible'

  assign lazyload_images = true
  if section.index == 1
    assign lazyload_images = false
  endif
-%}

<is-land {{ hydration }}>
  <slideshow-section section-id="{{ section.id }}">
    {%- unless full_width -%}
      <div class="page-width hero--padded">
    {%- endunless -%}

    {% style %}
      .hero--{{ section.id }} {
        {% comment %}height: {{ height }}px;{% endcomment %}
        border: none !important;
        border-bottom: none !important;
      }

      @media screen and (max-width: 768px) {
        .hero--{{ section.id }} {
          {% comment %}height: {{ height_mobile }}px;{% endcomment %}
        }
      }

      /* Hide all slideshow navigation elements */
      .hero--{{ section.id }} .flickity-prev-next-button {
        display: none !important;
      }

      .hero--{{ section.id }} .flickity-page-dots {
        display: none !important;
      }

      .hero--{{ section.id }} .slideshow__pause {
        display: none !important;
      }

      /* Remove any bottom borders from slideshow wrapper */
      #SlideshowWrapper-{{ section.id }} {
        border: none !important;
        border-bottom: none !important;
      }

      #SlideshowWrapper-{{ section.id }} .slideshow-wrapper {
        border: none !important;
        border-bottom: none !important;
      }

      /* Dual images layout styles */
      .hero__dual-images {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 3.333vw 5.208vw; /* 上下64px，左右100px，基于1920px计算 */
      }

      .hero__left-image {
        width: 33.698vw; /* 647px基于1920px计算 */
        height: 33.698vw; /* 647px基于1920px计算 */
        overflow: hidden;
        flex-shrink: 0;
      }

      .hero__right-image {
        width: 52.865vw; /* 1015px基于1920px计算 */
        height: 33.698vw; /* 647px基于1920px计算 */
        overflow: hidden;
        flex-shrink: 0;
      }

      .hero__left-image .hero__image,
      .hero__right-image .hero__image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .hero__image-link {
        display: block;
        width: 100%;
        height: 100%;
      }

      @media screen and (max-width: 1200px) {
        .hero__dual-images {
          padding: 2.5vw 3.75vw; /* 调整中等屏幕的间距 */
        }

        .hero__left-image {
          width: 33.333vw; /* 400px基于1200px计算 */
          height: 33.333vw; /* 400px基于1200px计算 */
        }

        .hero__right-image {
          width: 50vw; /* 600px基于1200px计算 */
          height: 33.333vw; /* 400px基于1200px计算 */
        }
      }

      @media screen and (max-width: 768px) {
        .hero__dual-images {
          flex-direction: column;
          height: auto;
          padding: 5.208vw; /* 40px基于768px计算 */
          gap: 2.604vw; /* 20px基于768px计算 */
        }

        .hero__left-image,
        .hero__right-image {
          width: 100%;
          height: 32.552vw; /* 250px基于768px计算 */
        }
      }
    {% endstyle %}

    <div id="SlideshowWrapper-{{ section.id }}">
      {%- if section.blocks.size > 0 -%}
        <div class="slideshow-wrapper">
          {%- if autoplay and style == 'bars' and section.blocks.size > 1 -%}
            {%- style -%}
              [data-bars][data-autoplay="true"] .flickity-page-dots .dot:after {
                animation-duration: {{ autoplay_speed | times: 1000 }}ms;
              }
            {%- endstyle -%}

            <button
              type="button"
              class="visually-hidden slideshow__pause"
              data-id="{{ section.id }}"
              aria-live="polite"
            >
              <span class="slideshow__pause-stop">
                {% render 'icon', name: 'pause' %}
                <span class="icon__fallback-text visually-hidden">
                  {% render 't_with_fallback', key: 'actions.slideshow_pause', fallback: 'Slideshow pause' -%}
                </span>
              </span>
              <span class="slideshow__pause-play">
                {% render 'icon', name: 'play' %}
                <span class="icon__fallback-text visually-hidden">
                  {% render 't_with_fallback', key: 'actions.slideshow_play', fallback: 'Slideshow play' -%}
                </span>
              </span>
            </button>
          {%- endif -%}

          <div
            id="Slideshow-{{ section.id }}"
            class="hero hero--{{ section.id }}{% if section.index == 1 %} loaded{% else %} loading{% endif %} loading--delayed"
            data-autoplay="{{ autoplay }}"
            data-speed="{{ autoplay_speed | times: 1000 }}"
            data-slide-count="{{ section.blocks.size }}"
          >
            {%- for block in section.blocks -%}
              <div
                {{ block.shopify_attributes }}
                class="slideshow__slide slideshow__slide--{{ block.id }}{% if section.index == 1 and forloop.index == 1 %} is-selected{% endif %}"
                data-index="{{ forloop.index0 }}"
                data-id="{{ block.id }}"
              >


                <div class="hero__dual-images">
                  <div class="hero__left-image">
                    {%- if block.settings.image != blank -%}
                      {%- if block.settings.link != blank -%}
                        <a href="{{ block.settings.link }}" class="hero__image-link" aria-hidden="true">
                      {%- endif -%}

                      {%- capture image_classes -%}
                          hero__image hero__image--{{ block.id }}
                        {%- endcapture -%}

                      {%- liquid
                        if forloop.index == 1
                          assign loading = lazyload_images
                        else
                          assign loading = true
                        endif
                      -%}
                      {%- render 'image-element',
                        img: block.settings.image,
                        loading: loading,
                        classes: image_classes,
                        sizeVariable: '647px'
                      -%}

                      {%- if block.settings.link != blank -%}
                        </a>
                      {%- endif -%}
                    {%- else -%}
                      {%- render 'placeholder-svg', name: 'lifestyle-1' -%}
                    {%- endif -%}
                  </div>

                  {%- comment -%} Right side static image - same for all slides {%- endcomment -%}
                  <div class="hero__right-image">
                    {%- if section.settings.right_image != blank -%}
                      {%- if section.settings.right_link != blank -%}
                        <a href="{{ section.settings.right_link }}" class="hero__image-link" aria-hidden="true">
                      {%- endif -%}

                      {%- capture right_image_classes -%}
                          hero__image hero__image--right-static
                        {%- endcapture -%}

                      {%- render 'image-element',
                        img: section.settings.right_image,
                        loading: lazyload_images,
                        classes: right_image_classes,
                        sizeVariable: '1015px'
                      -%}

                      {%- if section.settings.right_link != blank -%}
                        </a>
                      {%- endif -%}
                    {%- else -%}
                      {%- render 'placeholder-svg', name: 'lifestyle-2' -%}
                    {%- endif -%}
                  </div>
                </div>
              </div>
            {%- endfor -%}
          </div>
        </div>
      {%- endif -%}

      {%- render 'placeholder-noblocks' -%}
    </div>
    {%- unless full_width -%}
      </div>
    {%- endunless -%}
  </slideshow-section>

  <template data-island>
    <script type="module">
      import '@archetype-themes/modules/slideshow'
    </script>
  </template>
</is-land>
